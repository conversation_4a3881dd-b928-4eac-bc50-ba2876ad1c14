package com.laien.web.biz.proj.core.util;

import cn.hutool.core.collection.CollUtil;
import com.laien.common.core.enums.biz.CommonVideoDirectionEnums;
import com.laien.web.frame.exception.BizException;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 业务相关通用工具类
 *
 * <AUTHOR>
 * @since 2025/09/08
 */
public class BizCommonUtil {

    /**
     * 校验Workout中左右动作是否成对且顺序正确
     *
     * 校验规则：
     * 1. 必须保证一个workout中左右动作成对出现
     * 2. 同一个动作的左右必须先左后右
     * 3. 不限制左右动作必须连续出现（左右动作中间也能加动作，不管是单个或左右动作）
     *
     * @param videoInfos 视频信息列表
     * @return 校验结果
     * <AUTHOR>
     * @since 2025/09/08
     */
    public static boolean checkLeftRightVideos(List<WorkoutVideoInfo> videoInfos) {
        if (CollUtil.isEmpty(videoInfos)) {
            return true;
        }

        List<String> errors = new ArrayList<>();

        // 遍历每个视频，找到左动作并校验
        for (int i = 0; i < videoInfos.size(); i++) {
            WorkoutVideoInfo video = videoInfos.get(i);

            // 只处理左动作
            if (video.getDirection() != CommonVideoDirectionEnums.LEFT) {
                continue;
            }

            Integer leftVideoId = video.getVideoId();
            Integer rightVideoId = video.getRightVideoId();

            // 先检查整个列表中是否存在对应的右动作
            boolean rightVideoExists = false;
            boolean rightVideoAfterLeft = false;

            for (int j = 0; j < videoInfos.size(); j++) {
                WorkoutVideoInfo checkVideo = videoInfos.get(j);
                // 找到对应的右动作：videoId匹配且direction为RIGHT
                if (rightVideoId.equals(checkVideo.getVideoId()) && checkVideo.getDirection() == CommonVideoDirectionEnums.RIGHT) {
                    rightVideoExists = true;
                    // 检查右动作是否在左动作之后
                    if (j > i) {
                        rightVideoAfterLeft = true;
                    }
                    break;
                }
            }

            // 根据不同情况记录具体错误
            if (!rightVideoExists) {
                errors.add(String.format("左动作视频ID[%d]缺少对应的右动作视频ID[%d]", leftVideoId, rightVideoId));
            } else if (!rightVideoAfterLeft) {
                errors.add(String.format("左动作视频ID[%d]必须在右动作视频ID[%d]之前出现", leftVideoId, rightVideoId));
            }
        }

        return errors.isEmpty() ? true : throwErrorLines(errors);
    }

    private static boolean throwErrorLines(List<String> errors) {
        StringBuilder errorMessage = new StringBuilder("checkVideoLeftRightError:");
        for (String error : errors) {
            errorMessage.append(error).append("\n");
        }
        throw new BizException(errorMessage.toString());
    }


    /**
     * Workout视频信息
     * 用于左右动作校验的通用对象
     */
    @Data
    public static class WorkoutVideoInfo {
        private Integer videoId;
        private Integer rightVideoId;
        private CommonVideoDirectionEnums direction;

    }
}
